import os
from typing import Dict, Optional
import httpx
from fastapi import HTTPException

# Request context
from app.middlewares.request_context import request_context

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger(__name__)


class TypesenseService:

    def __init__(self):
        # Set module for contextual logging
        request_context.module_name = 'typesense_service'
        self.host = os.getenv("TYPESENSE_HOST", "localhost")
        self.port = os.getenv("TYPESENSE_PORT", "8108")
        self.protocol = os.getenv("TYPESENSE_PROTOCOL", "http")
        self.api_key = os.getenv("TYPESENSE_API_KEY")
        self.collection_name = os.getenv("TYPESENSE_COLLECTION_NAME", "products")
        self.freebies_collection_name = os.getenv("TYPESENSE_FREEBIES_COLLECTION_NAME", "freebies_products")

        if not self.api_key:
            raise ValueError("TYPESENSE_API_KEY environment variable is required")

        self.base_url = f"{self.protocol}://{self.host}:{self.port}"
        self.headers = {
            "X-TYPESENSE-API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
    
    async def get_product_by_sku(self, sku: str, facility_name: str) -> Optional[Dict]:
        try:
            # Note: facility_name from payload maps to facility_code field in Typesense
            search_params = {
                "q": sku,
                "query_by": "child_sku",
                "filter_by": f"child_sku:={sku} && facility_code:={facility_name}",
                "per_page": 1
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/collections/{self.collection_name}/documents/search",
                    headers=self.headers,
                    params=search_params,
                    timeout=10.0
                )

                if response.status_code == 200:
                    data = response.json()
                    hits = data.get("hits", [])

                    if hits:
                        product = hits[0]["document"]
                        logger.info(f"typesense_product_found | sku={sku} facility={facility_name}")
                        return product
                    else:
                        logger.warning(f"typesense_product_not_found | sku={sku} facility={facility_name}")
                        return None

                elif response.status_code == 404:
                    logger.warning(f"typesense_product_not_found_404 | sku={sku} facility={facility_name}")
                    return None
                else:
                    logger.error(f"typesense_api_error | sku={sku} facility={facility_name} status_code={response.status_code}")
                    raise HTTPException(
                        status_code=500, 
                        detail=f"Failed to fetch product data for SKU {sku}"
                    )

        except httpx.TimeoutException:
            logger.error(f"typesense_timeout | sku={sku} facility={facility_name}", exc_info=True)
            raise HTTPException(
                status_code=500, 
                detail=f"Timeout while fetching product data for SKU {sku}"
            )
        except Exception as e:
            logger.error(f"typesense_product_error | sku={sku} facility={facility_name} error={e}", exc_info=True)
            raise HTTPException(
                status_code=500, 
                detail=f"Error fetching product data for SKU {sku}"
            )

    async def get_freebie_by_sku(self, sku: str, facility_name: str) -> Optional[Dict]:
        """Get freebie product details by SKU from freebies_products collection"""
        try:
            search_params = {
                "q": sku,
                "query_by": "sku",
                "filter_by": f"sku:={sku} && facility_code:={facility_name}",
                "per_page": 1
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/collections/{self.freebies_collection_name}/documents/search",
                    headers=self.headers,
                    params=search_params,
                    timeout=10.0
                )

                if response.status_code == 200:
                    data = response.json()
                    hits = data.get("hits", [])

                    if hits:
                        freebie = hits[0]["document"]
                        logger.info(f"typesense_freebie_found | sku={sku} facility={facility_name}")
                        return freebie
                    else:
                        logger.warning(f"typesense_freebie_not_found | sku={sku} facility={facility_name}")
                        return None

                elif response.status_code == 404:
                    logger.warning(f"typesense_freebie_not_found_404 | sku={sku} facility={facility_name}")
                    return None
                else:
                    logger.error(f"typesense_freebie_api_error | sku={sku} facility={facility_name} status_code={response.status_code}")
                    raise HTTPException(
                        status_code=500, 
                        detail=f"Failed to fetch freebie data for SKU {sku}"
                    )

        except httpx.TimeoutException:
            logger.error(f"typesense_freebie_timeout | sku={sku} facility={facility_name}", exc_info=True)
            raise HTTPException(
                status_code=500, 
                detail=f"Timeout while fetching freebie data for SKU {sku}"
            )
        except Exception as e:
            logger.error(f"typesense_freebie_error | sku={sku} facility={facility_name} error={e}", exc_info=True)
            raise HTTPException(
                status_code=500, 
                detail=f"Error fetching freebie data for SKU {sku}"
            )

    def extract_item_fields(self, product: Dict) -> Dict:
        # Extract only required fields from product
        product_data = {
            "cgst": float(product.get("tax", 0.0)),  # tax maps to cgst
            "sgst": float(product.get("sgst", 0.0)),
            "igst": float(product.get("igst", 0.0)),
            "cess": float(product.get("cess", 0.0)),
            "selling_price_net": float(product.get("selling_price_net", 0.0)), # Price fields
            "is_returnable": bool(product.get("is_returnable", False)), # Return policy fields
            "return_type": str(product.get("return_type", "00")),
            "return_window": int(product.get("return_window", 0)),
            "wh_sku": str(product.get("wh_sku", "")),  # Stock fields
            "pack_uom_quantity": int(product.get("pack_uom_qty", 1)),
        }

        return product_data
