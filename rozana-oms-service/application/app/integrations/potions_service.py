import httpx
from typing import Dict, Any
from app.integrations.potions_config import get_potions_config
from app.core.constants import OrderStatus

# Request context
from app.middlewares.request_context import request_context

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger("potions_service")

# Redis token cache
from app.connections.redis_wrapper import RedisJSONWrapper

class PotionsService:
    """Service for integrating with Potions WMS API"""
    
    def __init__(self):
        self.config = get_potions_config()
        # annotate logs with module context
        request_context.module_name = 'potions_service'
        # Use Potions API configuration
        if self.config and self.config.integration_enabled:
            self.potions_base_url = self.config.potions_base_url
            self.client_id = self.config.client_id
            self.client_secret = self.config.client_secret
            self.timeout = float(self.config.timeout)
            self.token_cache_db = getattr(self.config, 'token_cache_db', 3)
        else:
            # Default values when integration is disabled
            self.potions_base_url = "http://localhost:8004"
            self.client_id = ""
            self.client_secret = ""
            self.timeout = 60.0
            self.token_cache_db = 3

        # Redis client for token caching
        self._redis = RedisJSONWrapper(database=self.token_cache_db)
        self._token_key = "potions:oauth:access_token"

    async def sync_order_by_id(self, facility_name: str, order_id: str, order_service) -> Dict[str, Any]:
        """Sync order to Potions WMS API by order ID"""
        try:
            request_context.module_name = 'potions_service'
            # Call Potions API to trigger WMS sync
            result = await self._trigger_potions_wms_sync(order_id)
            
            if result['success']:
                # Update order status to WMS_SYNCED (21)
                await order_service.update_order_status(order_id, OrderStatus.WMS_SYNCED)
                logger.info(
                    f"potions_wms_sync_success | order_id={order_id} facility_name={facility_name} task_id={result.get('task_id')}"
                )
                return {
                    "success": True,
                    "status": OrderStatus.WMS_SYNCED,
                    "task_id": result.get('task_id'),
                    "message": "Order synced to Potions WMS successfully"
                }
            else:
                # Update order status to WMS_SYNC_FAILED (22)
                await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
                logger.error(
                    f"potions_wms_sync_failed | order_id={order_id} facility_name={facility_name} error={result.get('error') or result.get('message')}"
                )
                return {
                    "success": False,
                    "status": OrderStatus.WMS_SYNC_FAILED,
                    "error": result.get('error'),
                    "message": f"Failed to sync order to Potions WMS: {result.get('message')}"
                }
                
        except Exception as e:
            # Update order status to WMS_SYNC_FAILED (22) on exception
            await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
            logger.error(
                f"potions_wms_sync_exception | order_id={order_id} facility_name={facility_name} error={e}",
                exc_info=True,
            )
            return {
                "success": False,
                "status": OrderStatus.WMS_SYNC_FAILED,
                "error": str(e),
                "message": "Exception occurred while syncing to Potions WMS"
            }
    
    async def _trigger_potions_wms_sync(self, order_id: str) -> Dict[str, Any]:
        """Trigger Potions WMS sync via API call (simple 2-attempt flow)."""
        try:
            request_context.module_name = 'potions_service'
            payload = {"order_id": order_id}
            endpoint = f"{self.potions_base_url}/api/potions/integrations/order/create/"
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                for attempt in range(2):
                    token = await self._get_oauth_token()
                    if not token:
                        return {"success": False, "error": "auth_failed", "message": "Unable to obtain OAuth token"}
                    headers = {
                        'Content-Type': 'application/json',
                        'Authorization': f'Bearer {token}'
                    }
                    resp = await client.post(endpoint, headers=headers, json=payload)
                    if resp.status_code in (200, 201, 202):
                        data = resp.json() if resp.content else {}
                        return {"success": True, "task_id": data.get('task_id'), "message": "Order sync triggered"}
                    if resp.status_code == 401 and attempt == 0:
                        # Invalidate cached token and retry once
                        try:
                            self._redis.delete(self._token_key)
                        except Exception:
                            pass
                        continue
                    # Non-401 or second failure
                    return {"success": False, "error": f"potions_api_{resp.status_code}", "message": resp.text}
        except Exception as e:
            logger.error(f"potions_wms_api_exception | order_id={order_id} error={e}", exc_info=True)
            return {"success": False, "error": str(e), "message": "Exception during Potions API call"}
    
    async def _get_oauth_token(self) -> str:
        """Return cached token or fetch and cache a new one (minimal flow)."""

        try:
            request_context.module_name = 'potions_service'
            # Try cache
            if self._redis and self._redis.connected:
                cached = self._redis.get(self._token_key)
                if cached:
                    return cached

            # Fetch new
            token_endpoint = f"{self.potions_base_url}/o/token/"
            token_data = {
                "grant_type": "client_credentials",
                "client_id": self.client_id,
                "client_secret": self.client_secret,
            }
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                resp = await client.post(token_endpoint, headers=headers, data=token_data)
                if resp.status_code != 200:
                    return None
                data = resp.json()
                token = data.get('access_token')
                exp = data.get('expires_in', 300)
                ttl = max(int(exp) - 60, 60) if isinstance(exp, int) else 300
                if token and self._redis and self._redis.connected:
                    try:
                        self._redis.set_with_ttl(self._token_key, token, ttl)
                    except Exception:
                        pass
                return token
        except Exception:
            return None
    
    async def cancel_outbound_order(self, order_reference: str, warehouse: str = None) -> Dict[str, Any]:
        """Cancel order in Potions WMS (placeholder for future implementation)"""
        request_context.module_name = 'potions_service'
        logger.info(
            f"potions_cancel_not_implemented | order_reference={order_reference} warehouse={warehouse}"
        )
        return {
            "success": True,
            "message": "Order cancellation not implemented in Potions WMS - handled in OMS only"
        }
    
    async def process_return(self, return_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process return in Potions WMS (placeholder for future implementation)"""
        request_context.module_name = 'potions_service'
        logger.info(
            f"potions_return_not_implemented | return_data_keys={list((return_data or {}).keys())}"
        )
        return {
            "success": True,
            "message": "Return processing not implemented in Potions WMS - handled in OMS only"
        }


# Create a singleton instance
potions_service = PotionsService()
