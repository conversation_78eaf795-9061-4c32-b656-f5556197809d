from fastapi import HTTPException
from app.connections.database import get_raw_transaction
from app.services.order_service import OrderService
from app.core.constants import OrderStatus
from sqlalchemy import text
from typing import List, Dict, Optional
from datetime import datetime, timedelta, timezone

# Services
from app.services.returns_service import ReturnsService

# Validators
from app.validations.returns import ReturnsValidator

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger("app.core.order_return")


async def create_return_core(order_id: str, items: Optional[List[Dict]] = None, order_full_return: bool = False, return_reason: Optional[str] = None):
    """
    Behavior:
    - If items are provided (non-empty): treat as partial return and return only those SKUs/quantities.
    - Else if order_full_return is True: fetch eligible items from DB and return them all (based on fulfilled statuses).
    - Else: 400 Bad Request.

    Args:
        order_id: OMS order ID
        items: Optional list of {sku, quantity} for partial return
        order_full_return: When true and items not provided, process full return
        return_reason: Optional reason
    """
    try:
        # Prepare items for full return if requested
        if order_full_return and not items:
            items = OrderService.get_return_eligible_items(order_id)  # may raise ValueError

        # Validate Layer
        with get_raw_transaction() as conn:
            # Ensure order exists and status allows returns (may raise ValueError)
            order_row, facility_name = ReturnsValidator.validate_order_exists_and_status(conn, order_id)

            # Build current_items map for existence/quantity checks
            get_items_sql = """
                SELECT id, sku, quantity, fulfilled_quantity, status, is_returnable, return_type, return_window
                FROM order_items
                WHERE order_id = :order_pk
            """
            items_result = conn.execute(text(get_items_sql), {'order_pk': order_row.id})
            current_items = {row.sku: row for row in items_result.fetchall()}

            # When items are provided (partial) or derived (full), validate them (may raise ValueError)
            if items and len(items) > 0:
                matched_rows = ReturnsValidator.validate_items_exist_and_quantities(current_items, items)
                ReturnsValidator.validate_items_eligibility(matched_rows)

            # Cache current status name before exiting DB context
            current_status_name = OrderStatus.get_customer_status_name(order_row.status)

        # If we have items persist return and respond
        if items and len(items) > 0:
            persisted = ReturnsService.create_return(order_id, items, return_reason)
            # Derive return type without separate variable per user's request
            ret_type = "full" if order_full_return else "partial"
            return {
                "success": True,
                "message": f"Return created for order {order_id}",
                "order_id": order_id,
                "return_reference": persisted["return_reference"],
                "return_type": ret_type,
                "returned_items": persisted["returned_items"],
                "total_refund_amount": persisted["total_refund_amount"],
                "order_status": current_status_name,
                "wms_status": None,
            }

        # Full return path fallback: if flag set but no items resolved/eligible
        if order_full_return:
            raise HTTPException(status_code=400, detail="No eligible items found to return for this order")

        # Neither items nor full-return flag provided
        raise HTTPException(status_code=400, detail="Provide items for partial return or set order_full_return=true for full return")
    except HTTPException:
        # Preserve intended HTTP statuses
        raise
    except ValueError as e:
        # Single mapping for domain/validation errors
        raise HTTPException(status_code=400, detail=str(e))